<template>
  <div class="sale-list-drawer">
    <button class="btn-name" @click="showDrawer" :aria-label="btnName">
      {{ btnName }}
    </button>
    <uni-drawer ref="showRight" mode="right" :width="360" :mask-click="false">
      <view class="search-container">
        <uni-search-bar
          v-model="searchKeyword"
          radius="5"
          placeholder="请输入销售人员姓名"
          clearButton="auto"
          cancelButton="none"
          @input="handleSearchInput"
          @confirm="handleSearchConfirm"
          @clear="handleSearchClear"
        />
      </view>
      <view class="scroll-view">
        <scroll-view class="scroll-view-box" scroll-y="true">
          <!-- 显示搜索结果或原始分组列表 -->
          <template v-if="isSearching && searchResults.length > 0">
            <view class="search-results">
              <view class="search-result-header">
                <text class="result-count">找到 {{ searchResults.length }} 个结果</text>
              </view>
              <view
                v-for="item in searchResults"
                :key="item.id"
                class="search-result-item"
                :class="{ 'selected': isItemSelected(item.id) }"
                @click="handleSearchResultClick(item)"
              >
                <text class="result-name">{{ item.name }}</text>
                <view v-if="isItemSelected(item.id)" class="selected-icon">
                  <uni-icons type="checkbox-filled" size="20" color="var(--THEME-COLOR)"></uni-icons>
                </view>
              </view>
            </view>
          </template>
          <template v-else-if="isSearching && searchResults.length === 0 && searchKeyword.trim()">
            <view class="no-results">
              <text class="no-results-text">未找到匹配的销售人员</text>
              <text class="no-results-tip">请尝试其他关键词</text>
            </view>
          </template>
          <template v-else>
            <uni-indexed-list :options="displayOptions" @click="bindClick"></uni-indexed-list>
          </template>
        </scroll-view>
      </view>
    </uni-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue'
import pinyin from 'tiny-pinyin';

// 类型定义
interface SaleItem {
  id: string | number
  name: string
}

interface GroupedItem {
  letter: string
  data: string[]
  ids: (string | number)[]
}

const BUTTON_NAME = '请选择销售人员'

// Props 定义
const props = defineProps<{
  list: SaleItem[]
  selectedId?: string | number | null // 当前选中的销售人员ID
}>()

// Emits 定义
const emits = defineEmits<{
  change: [data: { id: string | number | null; name: string }]
}>()

// 响应式数据
const searchKeyword = ref('')
const searchResults = ref<SaleItem[]>([])
const isSearching = ref(false)

// 选中状态相关
const isItemSelected = (itemId: string | number): boolean => {
  return props.selectedId !== null && props.selectedId !== undefined && props.selectedId === itemId
}

// 根据选中的ID更新按钮显示文本
const updateButtonText = () => {
  if (props.selectedId) {
    const selectedItem = props.list.find(item => item.id === props.selectedId)
    if (selectedItem) {
      btnName.value = selectedItem.name
    } else {
      btnName.value = BUTTON_NAME
    }
  } else {
    btnName.value = BUTTON_NAME
  }
}

// 优化的分组函数 - 添加错误处理和性能优化
const groupByFirstLetter = (list: SaleItem[]): GroupedItem[] => {
  if (!Array.isArray(list) || list.length === 0) {
    return []
  }

  const map: Record<string, GroupedItem> = {}

  list.forEach(item => {
    if (!item?.name) return // 跳过无效数据

    try {
      // 获取首字母，添加错误处理
      const firstChar = item.name.charAt(0).trim() || '#'
      const pinyinResult = pinyin.convertToPinyin(firstChar, '', true)

      const firstLetter = (pinyinResult[0]?.[0] || firstChar).toUpperCase()

      if (!map[firstLetter]) {
        map[firstLetter] = {
          letter: firstLetter,
          data: [],
          ids: []
        }
      }

      map[firstLetter].data.push(item.name)
      map[firstLetter].ids.push(item.id)
    } catch (error) {
      console.warn('Pinyin conversion failed for:', item.name, error)
      // 降级处理：使用原始字符
      const fallbackLetter = item.name.charAt(0).toUpperCase()
      if (!map[fallbackLetter]) {
        map[fallbackLetter] = {
          letter: fallbackLetter,
          data: [],
          ids: []
        }
      }
      map[fallbackLetter].data.push(item.name)
      map[fallbackLetter].ids.push(item.id)
    }
  })

  return Object.values(map).sort((a, b) => a.letter.localeCompare(b.letter))
}

// 计算属性 - 缓存分组结果
const options = computed(() => groupByFirstLetter(props.list))

// 显示的选项（搜索时显示搜索结果，否则显示分组列表）
const displayOptions = computed(() => {
  return isSearching.value ? [] : options.value
})

// 抽屉引用和控制
const showRight = ref<any>(null)
const btnName = ref(BUTTON_NAME)

const showDrawer = () => {
  if (showRight.value) {
    if (btnName.value === BUTTON_NAME) {
      searchKeyword.value = ''
    } else {
      searchKeyword.value = btnName.value
    }
    showRight.value.open()
  }
}

// 搜索功能实现
const performSearch = (keyword: string) => {
  if (!keyword.trim()) {
    searchResults.value = []
    isSearching.value = false
    return
  }

  isSearching.value = true
  const lowerKeyword = keyword.toLowerCase().trim()

  // 多字段搜索：支持姓名、拼音首字母、全拼搜索
  searchResults.value = props.list.filter(item => {
    if (!item?.name) return false

    const name = item.name.toLowerCase()

    // 1. 直接姓名匹配
    if (name.includes(lowerKeyword)) {
      return true
    }

    // 2. 拼音首字母匹配
    try {
      const firstLetters = pinyin.convertToPinyin(item.name, '', true).toLowerCase()

      if (firstLetters.includes(lowerKeyword)) {
        return true
      }

      // 3. 全拼匹配
      const fullPinyin = pinyin.convertToPinyin(item.name).toLowerCase()

      if (fullPinyin.includes(lowerKeyword)) {
        return true
      }
    } catch (error) {
      console.warn('Search pinyin conversion failed for:', item.name, error)
    }

    return false
  })
}

// 防抖搜索
let searchTimer: number | null = null
const handleSearchInput = (e: any) => {
  const keyword = e.detail?.value || e.target?.value || e
  searchKeyword.value = keyword

  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 设置新的防抖定时器
  searchTimer = setTimeout(() => {
    performSearch(keyword)
  }, 300) // 300ms 防抖
}

const handleSearchConfirm = (e: any) => {
  const keyword = e.detail?.value || searchKeyword.value
  performSearch(keyword)
}

const handleSearchClear = () => {
  searchKeyword.value = ''
  searchResults.value = []
  isSearching.value = false
  if (searchTimer) {
    clearTimeout(searchTimer)
    searchTimer = null
  }
}

// 点击事件处理
const bindClick = (e: any) => {
  const { key, name } = e.item
  if (!key || !name) return

  const option = options.value.find((item) => item.letter === key)

  if (option) {
    const nameIndex = option.data.indexOf(name)
    if (nameIndex !== -1) {
      const id = option.ids[nameIndex]
      handleItemSelection(id, name)
    }
  }
}

// 统一的选择处理函数
const handleItemSelection = (id: string | number, name: string) => {
  // 检查是否点击的是已选中的项目
  const isCurrentlySelected = isItemSelected(id)

  if (isCurrentlySelected) {
    // 如果点击的是已选中的项目，则取消选择
    btnName.value = BUTTON_NAME
    emits('change', { id: null, name: '' })
  } else {
    // 如果点击的是未选中的项目，则选中它
    btnName.value = name
    emits('change', { id, name })
  }

  // 关闭抽屉
  if (showRight.value) {
    showRight.value.close()
  }
  // 清空搜索状态
  handleSearchClear()
}

// 搜索结果点击处理
const handleSearchResultClick = (item: SaleItem) => {
  handleItemSelection(item.id, item.name)
}

// 监听列表变化，清空搜索状态
watch(() => props.list, () => {
  handleSearchClear()
}, { deep: true })

// 监听选中ID变化，更新按钮文本
watch(() => props.selectedId, () => {
  updateButtonText()
}, { immediate: true })

// 监听列表变化，也需要更新按钮文本
watch(() => props.list, () => {
  updateButtonText()
}, { deep: true, immediate: true })

// 组件卸载时清理定时器
onUnmounted(() => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
})
</script>

/**
  * @path: /Users/<USER>/Git/uni-member-fe/node-modules/@dcloudio/uni-ui/lib/uni-indexed-list/uni-indexed-list-item.wxss
  * @description: ✅ 改源码样式, ui 就是要改, 这是巫术 border-bottom-color: #f6f6f8; 
  */

<style lang="scss" scoped>
.sale-list-drawer {
  width: 100%;
  height: 100%;
}

.search-container {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.scroll-view {
  /* #ifndef APP-NVUE */
  width: 100%;
  height: 100%;
  /* #endif */
  flex: 1;
}

// 处理抽屉内容滚动
.scroll-view-box {
  flex: 1;
  position: absolute;
  top: 140rpx; // 增加高度以适应搜索框
  right: 0;
  bottom: 0;
  left: 0;
}

.btn-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  line-height: 40rpx;
  background: none;
  border: none;
  padding: 0;
  text-align: left;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

// 搜索结果样式
.search-results {
  padding: 20rpx;
}

.search-result-header {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
}

.result-count {
  font-size: 24rpx;
  color: #666;
}

.search-result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: #f8f9fa;
  }

  &:active {
    background-color: #e9ecef;
  }

  &:last-child {
    border-bottom: none;
  }

  // 选中状态样式
  &.selected {
    background-color: #e3f2fd;
    border-left: 4rpx solid var(--THEME-COLOR);

    &:hover {
      background-color: #bbdefb;
    }
  }
}

.result-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.selected-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 无结果样式
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.no-results-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.no-results-tip {
  font-size: 24rpx;
  color: #999;
}

// 响应式设计
@media (max-width: 750rpx) {
  .search-result-item {
    padding: 25rpx 15rpx;
  }

  .result-name {
    font-size: 26rpx;
  }

  .no-results {
    padding: 80rpx 30rpx;
  }
}
</style>
