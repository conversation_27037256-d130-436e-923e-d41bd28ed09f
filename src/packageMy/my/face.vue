<template>
  <view class="face-box theme-bg">
    <template v-if="canUpFace">
      <view>
        <image class="demo-img" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/face-demo.png" />
      </view>
      <view class="facepowerd-box">
        <view class="box-tit">允许使用门店</view>
        <view>
          <checkbox-group class="checkbox-group" @change="checkListChange">
            <label v-for="item in faceBusInfo.face_check_list" :key="item.bus_id" class="facepowerd-item">
              {{ item.name }}
              <checkbox class="cb-transform" :value="item.bus_id" :checked="busIdList.includes(item.bus_id + '')" />
            </label>
          </checkbox-group>
        </view>
      </view>
      <label class="fixed-protocol-col theme-bg">
        <checkbox-group class="checkbox-group" @change="checkboxChange">
          <checkbox value="1" :checked="agreeChecked">《隐私政策》</checkbox>
        </checkbox-group>
      </label>
      <view class="fixed-bottom-wrap theme-bg">
        <button class="normal-btn" @tap="toCropper">开始录入</button>
      </view>
    </template>
    <view v-if="uploadFacePower && !canUpFace" class="face-up-box" @tap="canUpFace = true">
      <uni-icons class="up-mark" type="plusempty" size="42" color="#FF7427" />
      <view class="tit oth-color">点击上传</view>
    </view>
    <view v-if="!uploadFacePower && !canUpFace" class="face-up-box">
      <image class="up-img" src="/static/img/exclamation.png" />
      <view class="tit">暂无上传照片权限</view>
      <view class="des oth-color">如需上传请联系场馆</view>
    </view>
    <view class="facepowerd-box">
      <view v-if="!canUpFace" class="box-tit tips">
        <text>人脸进场许可</text>
        <view v-if="isUpload" class="tips-right">
          <text>已开通</text>
        </view>
        <view v-else class="tips-right">
          <text class="not-opened">未开通</text>
        </view>
      </view>
      <view v-if="!canUpFace && faceBusInfo.face_bus_list.length" class="box-tit">会员可使用人脸入场的场馆</view>
      <view v-if="!canUpFace && faceBusInfo.face_bus_list.length" class="box-tips">通卡场馆进行一次签到后人脸进场将自动开启，或主动授权开启人脸进场</view>
      <view v-if="!canUpFace && faceBusInfo.face_bus_list.length">
        <view v-for="item in faceBusInfo.face_bus_list" :key="item.name" class="facepowerd-item">
          <span>{{ item.name }}<span class="cur-tag" v-if="item.bus_id === loginUserInfo.bus_id">当前场馆</span></span>
          <uni-icons v-if="item.bind_face" type="checkbox-filled" size="24" color="#FF7427" />
          <button class="right-btn" @tap="goFaceAuth(item)" v-else>
            去授权
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="index">
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin'
const { checkLogin } = useLogin()
const agreementId = ref('')
const agreeChecked = ref(false)
const uploadFacePower = ref(false)
const isUpload = ref(false)
const canUpFace = ref(false)
const loginUserInfo = reactive({
  bus_id: '',
  m_id: '',
  user_id: '',
})

function toCropper() {
  if (agreeChecked.value) {
    uni.navigateTo({
      url: '/packageMy/my/cropper?selectedItem=' + JSON.stringify(selectedItem.value),
    })
  } else {
    uni.showToast({ title: '请先确认协议', icon: 'none' })
  }
}
async function checkboxChange(e) {
  agreeChecked.value = e.detail.value[0] === '1' ? true : false
  if (!agreeChecked.value && agreementId.value) {
    cancelAgreementLog()
  } else if (agreeChecked.value) {
    goAgreePage()
  }
}
async function goAgreePage() {
  await nextTick()
  agreeChecked.value = false
  uni.navigateTo({
    url: '/packageMy/agreement/private?showAgree=1',
    events: {
      acceptAgreementStatus: function (data) {
        if (data.isAgreeStatus == 1) {
          agreeChecked.value = true
          upAgreementLog()
        } else {
          agreeChecked.value = false
        }
      },
    },
  })
}
function cancelAgreementLog() {
  http
    .post('Personalcenter/delFaceAgreement', {
      id: agreementId.value,
    })
    .then((res) => {
      agreementId.value = ''
    })
}
function upAgreementLog() {
  http
    .post('Personalcenter/addFaceAgreement', {
      ...loginUserInfo,
      platform: 1,
      type: 1,
      version: '1.0.0',
    })
    .then((res) => {
      agreementId.value = res.data.id
    })
}
const faceBusInfo = reactive<{
  face_bus_list: Record<string, any>[]
  face_check_list: Record<string, any>[]
}>({
  face_bus_list: [],
  face_check_list: [],
})
function getFaceBindList() {
  http.get('Personalcenter/getFaceBindList', loginUserInfo).then((res) => {
    faceBusInfo.face_bus_list = res.data.face_bus_list
    faceBusInfo.face_check_list = res.data.face_check_list
    uploadFacePower.value = res.data.upload_face_power
    isUpload.value = res.data.is_upload
    if (!busIdList.value.length) {
      busIdList.value = res.data.face_check_list.map((item) => {
        return item.bus_id + ''
      })
      selectedItem.value = res.data.face_check_list
    }
  })
}
const selectedItem = ref<Record<string, any>[]>([])
const busIdList = ref<string[]>([])
function checkListChange(e) {
  busIdList.value = e.detail.value
  selectedItem.value = faceBusInfo.face_check_list.filter((item) => {
    return busIdList.value.includes(item.bus_id + '')
  })
}
function goFaceAuth(info) {
  uni.navigateTo({
    url: `/packageMy/my/faceAuth?bus_id=${info.bus_id}&bus_name=${info.name}&user_id=${info.user_id}`,
  })
}
onShow(() => {
  checkLogin().then((info) => {
    Object.assign(loginUserInfo, info)
    getFaceBindList()
  })
})
// 处理: 离开前上一个路由不是"我的"那么将会返回至"我的"
onUnload(() => {
  const pages = getCurrentPages()
  const page = pages[pages.length - 2]
  const path = page.route
  if (path && path !== '/pages/my/index') {
    uni.switchTab({
      url: '/pages/my/index',
    })
  }
})
</script>

<style lang="scss" scoped>
.face-box {
  width: 100%;
  height: 100%;
  padding-bottom: 180rpx;
  overflow-y: scroll;
  box-sizing: border-box;
  text-align: center;
  .con-wrap {
    padding: 10rpx 10rpx 100rpx;
  }
  .up-mark {
    margin-top: 90rpx;
    margin-bottom: 40rpx;
  }
  .up-img {
    width: 14rpx;
    height: 66rpx;
    margin-top: 90rpx;
    margin-bottom: 40rpx;
  }
  .success-tit {
    font-size: 36rpx;
    font-weight: bold;
  }
  .success-con {
    font-size: 28rpx;
    margin-top: 32rpx;
  }
}

.demo-img {
  width: 600rpx;
  height: 752rpx;
  margin: 0 auto;
  background: #fff;
}
.protocol-col {
  position: fixed;
  left: 0;
  bottom: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  color: #7d7d7d;
  width: 100%;
  display: flex;
  background: #fff;
  padding-left: 30rpx;
}
.facepowerd-box {
  margin: 40rpx;
}
.face-up-box {
  width: 360rpx;
  height: 360rpx;
  margin: 50rpx auto;
  border: 2rpx dashed #b4b4b4;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  .oth-color {
    color: $theme-text-color-other;
  }
  .tit {
    font-size: 30rpx;
    margin-bottom: 20rpx;
  }
  .des {
    font-size: 24rpx;
  }
}
.facepowerd-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding-left: 20rpx;
}

.tips {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .tips-right {
    display: flex;
    align-items: center;
    justify-content: center;
    // font-size: 28rpx;
    color: $theme-text-color-other;
    font-weight: bold;
    image {
      width: 34rpx;
      height: 30rpx;
      margin: 0;
      margin-left: 18rpx;
    }
  }
}

.not-opened {
  color: $uni-color-error;
}
.box-tips {
  color: $uni-text-color-grey;
  font-size: 24rpx;
  text-align: left;
  margin-bottom: 20rpx;
}
.right-btn {
  width: 144rpx;
  height: 50rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  line-height: 50rpx;
  background: var(--THEME-COLOR);
  margin: 0;
  font-weight: bold;
  font-size: 26rpx;
  color: #000;
}
.cur-tag {
  height: 35rpx;
  line-height: 35rpx;
  padding: 0 8rpx;
  font-size: 24rpx;
  display: inline-block;
  vertical-align: middle;
  margin-left: 8rpx;
  background: #ffffff;
  color: #fff;
  background: $theme-text-color-other;
  border-radius: 6rpx;
}
</style>
