<template>
  <view class="face-box">
    <bt-cropper ref="cropperComponent" :image-src="src" :ratio="1" mode="fixed" :d-width="500"> </bt-cropper>
    <view class="fixed-bottom-wrap">
      <button class="normal-btn" :disabled="isSubmiting" @tap="handleSaveClick">上传人脸</button>
    </view>
  </view>
</template>

<script setup lang="ts" name="index">
import http from '@/utils/request'
import env from '@/config/env'
import btCropper from '@/components/bt-cropper/bt-cropper.vue'
const loadoOptions = reactive({
  selectedItem: [],
})
const cropperComponent = ref()
const isSubmiting = ref(false)
const src = ref('')
onLoad((options) => {
  loadoOptions.selectedItem = JSON.parse(options.selectedItem || '[]')
})
onMounted(() => {
  handleChooseImage()
})
function handleChooseImage() {
  uni.chooseMedia({
    count: 1,
    mediaType: ['image'],
    success: (rst) => {
      // 设置url的值，显示控件
      src.value = rst.tempFiles[0].tempFilePath
    },
  })
}
function handleSaveClick() {
  if (!src.value) {
    handleChooseImage()
    return false
  }
  cropperComponent.value.crop().then(([err, resInfo]) => {
    if (!err) {
      uni.showModal({
        title: '是否确认上传头像？',
        content: '上传后将无法修改，如需修改请联系场馆前台。',
        cancelText: '暂不上传',
        confirmText: '确认上传',
        success: (resCon) => {
          if (!resCon.confirm || isSubmiting.value) {
            return false
          }
          uni.showLoading({
            title: '上传中',
            mask: true,
          })
          isSubmiting.value = true
          uni.uploadFile({
            url: `${env.apiBaseUrl}Personalcenter/uploadFaceBatch`,
            filePath: resInfo.tempFilePath,
            header: http.getGatewayParams(),
            fileType: 'image',
            name: 'rocketbird',
            formData: {
              face_data: JSON.stringify(loadoOptions.selectedItem),
            },
            success: function (res) {
              const data = JSON.parse(res.data || '')
              if (data.errorcode === 0) {
                uni.showToast({
                  title: data.errormsg,
                })
                uni.hideLoading()
                uni.switchTab({
                  url: '/pages/my/index',
                })
              } else {
                uni.hideLoading()
                uni.showToast({
                  title: data.errormsg,
                  icon: 'none',
                })
              }
            },
            fail: function () {
              uni.showToast({
                title: '微信版本过低，不支持上传图片！',
                icon: 'none',
              })
            },
            complete: function (res) {
              isSubmiting.value = false
            },
          })
        },
      })
    } else {
      uni.showToast({
        title: '图片裁剪错误！',
        icon: 'none',
      })
    }
  })
}
</script>

<style lang="scss">
.face-box {
  height: 100vh;
}
</style>
